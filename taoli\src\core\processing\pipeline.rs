/// 事件处理流水线
/// 
/// 负责原始事件的接收、解析和分发处理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::thread;
use std::time::Duration;

use anyhow::{Result, anyhow};
use crossbeam_channel::{bounded, unbounded, Receiver, Sender, TryRecvError};
use log::{info, debug, error};

use crate::core::network::grpc_receiver::GrpcReceiver;
use crate::core::processing::parser_pool::ParserPool;
use crate::core::storage::publisher_pool::PublisherPool;
use crate::core::types::{*, get_config};

/// 终极零延迟流水线引擎
pub struct UltimatePipeline {
    config: PipelineConfig,
    metrics: Arc<RealTimeMetrics>,
    running: Arc<AtomicBool>,
    
    // 队列
    parse_queue: (crossbeam_channel::Sender<RawTransaction>, crossbeam_channel::Receiver<RawTransaction>),
    publish_queue: (crossbeam_channel::Sender<ParsedEvent>, crossbeam_channel::Receiver<ParsedEvent>),
    
    // 组件
    grpc_receiver: Option<GrpcReceiver>,
    parser_pool: Option<ParserPool>,
    publisher_pool: Option<PublisherPool>,
}

impl UltimatePipeline {
    pub fn new(config: PipelineConfig) -> Self {
        let metrics = Arc::new(RealTimeMetrics::new());
        let running = Arc::new(AtomicBool::new(false));
        
        // 创建无锁队列
        let parse_queue = unbounded::<RawTransaction>();
        let publish_queue = unbounded::<ParsedEvent>();
        
        Self {
            config,
            metrics,
            running,
            parse_queue,
            publish_queue,
            grpc_receiver: None,
            parser_pool: None,
            publisher_pool: None,
        }
    }
    
    /// 启动流水线，带自动重启机制
    pub async fn start(&mut self) -> Result<()> {
        self.running.store(true, Ordering::Relaxed);
        
        info!("[流水线] 启动处理引擎");
        
        // 启动监控
        self.metrics.start_reporting();
        
        // 创建并启动发布器集群
        let publisher_pool = PublisherPool::new(
            self.publish_queue.1.clone(),
            self.config.clone(),
            Arc::clone(&self.metrics),
        );
        let publisher_senders = publisher_pool.get_senders();
        self.publisher_pool = Some(publisher_pool);
        
        // 创建并启动解析器集群
        let parser_pool = ParserPool::new(
            self.parse_queue.1.clone(),
            publisher_senders,
            self.config.clone(),
            Arc::clone(&self.metrics),
            None, // 旧的pipeline不使用统一发布器
        );
        let parser_senders = parser_pool.get_senders();
        self.parser_pool = Some(parser_pool);
        
        // 创建gRPC接收器
        let grpc_receiver = GrpcReceiver::new(
            parser_senders,
            Arc::clone(&self.metrics),
        );
        self.grpc_receiver = Some(grpc_receiver);
        
        info!("[流水线] 组件初始化完成，开始连接数据源");
        
        // 启动gRPC接收器，带自动重试
        let endpoint = &get_config().grpc.endpoint;
        
        // 无限重试循环
        loop {
            if !self.running.load(Ordering::Relaxed) {
                break;
            }
            
            info!("[连接] 启动gRPC接收器...");
            if let Some(ref receiver) = self.grpc_receiver {
                match receiver.start_with_retry(endpoint).await {
                    Ok(_) => {
                        info!("[连接] gRPC接收器正常退出");
                        break;
                    }
                    Err(e) => {
                        error!("[连接] gRPC接收器失败: {}", e);
                        
                        if self.running.load(Ordering::Relaxed) {
                            info!("[连接] {}秒后重试连接...", get_config().grpc.reconnect_interval);
                            tokio::time::sleep(tokio::time::Duration::from_secs(get_config().grpc.reconnect_interval)).await;
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 优雅关闭流水线
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("[流水线] 正在关闭流水线...");
        self.running.store(false, Ordering::Relaxed);
        
        // 等待所有工作线程完成
        if let Some(parser_pool) = self.parser_pool.take() {
            for worker in parser_pool.workers {
                let _ = worker.join();
            }
        }
        
        if let Some(publisher_pool) = self.publisher_pool.take() {
            for worker in publisher_pool.workers {
                let _ = worker.join();
            }
        }
        
        info!("[流水线] 流水线已关闭");
        Ok(())
    }
    
    /// 获取监控指标
    pub fn get_metrics(&self) -> &RealTimeMetrics {
        &self.metrics
    }
    
    /// 检查是否运行中
    pub fn is_running(&self) -> bool {
        self.running.load(Ordering::Relaxed)
    }
    
    /// 设置运行状态
    pub fn set_running(&self, running: bool) {
        self.running.store(running, Ordering::Relaxed);
    }
}