/// Pump插件类型定义
/// 
/// 定义Pump解析过程中使用的数据结构和枚举类型

pub mod constants;

use serde::{Deserialize, Serialize};
use solana_program::pubkey::Pubkey;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use borsh::{BorshDeserialize, BorshSerialize};

/// 可选布尔值（对应IDL中的OptionBool类型）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize, BorshDeserialize, BorshSerialize)]
pub struct OptionBool(pub bool);

/// PumpFun交易类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PumpTransactionType {
    /// 初始化
    Initialize,
    /// 设置参数
    SetParams,
    /// 创建代币
    Create,
    /// 购买代币
    Buy,
    /// 出售代币
    Sell,
    /// 提取
    Withdraw,
    /// 未知类型
    Unknown,
}

impl From<&str> for PumpTransactionType {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "initialize" => Self::Initialize,
            "setparams" => Self::SetParams,
            "create" => Self::Create,
            "buy" => Self::Buy,
            "sell" => Self::Sell,
            "withdraw" => Self::Withdraw,
            _ => Self::Unknown,
        }
    }
}

/// 交换方向
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SwapDirection {
    /// 买入（SOL -> Token）
    Buy,
    /// 卖出（Token -> SOL）
    Sell,
}

/// 代币信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TokenInfo {
    /// 代币mint地址
    pub mint: Pubkey,
    /// 代币名称
    pub name: Option<String>,
    /// 代币符号
    pub symbol: Option<String>,
    /// 代币URI
    pub uri: Option<String>,
    /// 小数位数
    pub decimals: u8,
    /// 总供应量
    pub total_supply: Option<u64>,
}

/// 价格信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 代币价格 (SOL/token)
    pub token_price: Decimal,
    /// 交易金额 (SOL)
    pub amount_sol: Decimal,
    /// 交易数量 (token)
    pub amount_token: Decimal,
    /// 当前总供应量
    pub current_supply: Option<u64>,
}

/// 费用信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FeeInfo {
    /// 费用接收方
    pub fee_recipient: Option<Pubkey>,
    /// 费用比例（基点）
    pub fee_basis_points: Option<u16>,
    /// 费用金额（SOL）
    pub fee_amount: Option<Decimal>,
    /// 创作者费用接收方
    pub creator_fee_recipient: Option<Pubkey>,
    /// 创作者费用比例（基点）
    pub creator_fee_basis_points: Option<u16>,
    /// 创作者费用金额（SOL）
    pub creator_fee_amount: Option<Decimal>,
    /// 创作者金库地址
    pub creator_vault: Option<Pubkey>,
}

/// 交易详情
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TransactionDetails {
    /// 交易签名
    pub signature: String,
    /// 交易类型
    pub transaction_type: PumpTransactionType,
    /// 用户地址
    pub user: Option<Pubkey>,
    /// 代币信息
    pub token_info: Option<TokenInfo>,
    /// 交易方向
    pub swap_direction: Option<SwapDirection>,
    /// SOL金额
    pub sol_amount: Option<Decimal>,
    /// 代币数量
    pub token_amount: Option<Decimal>,
    /// 价格信息
    pub price_info: Option<PriceInfo>,
    /// 费用信息
    pub fee_info: Option<FeeInfo>,
    /// 时间戳
    pub timestamp: Option<DateTime<Utc>>,
    /// 是否完成
    pub is_complete: bool,
}

impl Default for TransactionDetails {
    fn default() -> Self {
        Self {
            signature: String::new(),
            transaction_type: PumpTransactionType::Unknown,
            user: None,
            token_info: None,
            swap_direction: None,
            sol_amount: None,
            token_amount: None,
            price_info: None,
            fee_info: None,
            timestamp: None,
            is_complete: false,
        }
    }
}