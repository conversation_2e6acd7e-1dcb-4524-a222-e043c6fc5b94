// 这是一个新文件: newcopy_v2/src/services/sol_price_oracle.rs
use std::sync::{Arc, atomic::{AtomicU64, Ordering}};
use std::time::Duration;
use tracing::{info, warn, error, debug};
use serde::Deserialize;

const JUPITER_API_URL: &str = "https://price.jup.ag/v4/price?ids=SOL&vsToken=USDC";
const UPDATE_INTERVAL_SECS: u64 = 5; // 每5秒更新一次价格
const PRICE_PRECISION_FACTOR: f64 = 1_000_000.0;

// ---- 多数据源 ----
const COINGECKO_URL: &str = "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd";
const BINANCE_URL: &str = "https://api.binance.com/api/v3/ticker/price?symbol=SOLUSDT";
const OKX_URL: &str = "https://www.okx.com/api/v5/market/ticker?instId=SOL-USDT";

#[derive(Deserialize, Debug)]
struct JupiterPriceResponse {
    data: Data,
}

#[derive(Deserialize, Debug)]
struct Data {
    #[serde(rename = "SOL")]
    sol: SolData,
}

#[derive(Deserialize, Debug)]
struct SolData {
    price: f64,
}

/// 负责获取并缓存SOL的实时美元价格的服务
#[derive(Debug, Clone)]
pub struct SolPriceOracle {
    // 将价格存储为乘以1,000,000的u64，以避免在原子操作中使用f64
    price_usd_atomic: Arc<AtomicU64>, 
}

impl SolPriceOracle {
    /// 创建一个新的价格预言机实例
    pub fn new() -> Self {
        Self {
            price_usd_atomic: Arc::new(AtomicU64::new(0)),
        }
    }
    
    /// 启动后台任务，定期从Jupiter API更新价格
    pub async fn start(&self) {
        let oracle_clone = self.clone();
        info!("SOL价格预言机服务已启动，每 {} 秒更新一次。", UPDATE_INTERVAL_SECS);

        // 立即执行一次以获取初始价格
        match oracle_clone.update_price().await {
            Ok(_) => {
                let p = oracle_clone.get_price_usd();
                info!("SOL美元价格预言机已就绪，首次价格: ${}", p);
            }
            Err(e) => {
                error!("首次获取SOL价格失败: {}。将在后台重试。", e);
            }
        }

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(UPDATE_INTERVAL_SECS));
            loop {
                interval.tick().await;
                if let Err(e) = oracle_clone.update_price().await {
                    warn!("更新SOL价格失败: {}", e);
                }
            }
        });
    }

    /// 获取当前缓存的SOL价格 (USD)
    pub fn get_price_usd(&self) -> f64 {
        let price_u64 = self.price_usd_atomic.load(Ordering::Relaxed);
        price_u64 as f64 / PRICE_PRECISION_FACTOR
    }
    
    /// 从API获取并更新价格的内部函数
    async fn update_price(&self) -> Result<(), reqwest::Error> {
        // 按优先级顺序尝试多个数据源
        if let Some(price) = Self::fetch_from_coingecko().await? {
            self.store_price(price);
            return Ok(());
        }
        if let Some(price) = Self::fetch_from_binance().await? {
            self.store_price(price);
            return Ok(());
        }
        if let Some(price) = Self::fetch_from_okx().await? {
            self.store_price(price);
            return Ok(());
        }
        // 兜底使用 Jupiter
        let response: JupiterPriceResponse = reqwest::get(JUPITER_API_URL).await?.json().await?;
        self.store_price(response.data.sol.price);
        Ok(())
    }

    fn store_price(&self, new_price: f64) {
        let price_u64 = (new_price * PRICE_PRECISION_FACTOR) as u64;
        self.price_usd_atomic.store(price_u64, Ordering::Relaxed);
        debug!("SOL美元价格已更新: ${}", new_price);
    }

    async fn fetch_from_coingecko() -> Result<Option<f64>, reqwest::Error> {
        let resp = reqwest::get(COINGECKO_URL).await?;
        if !resp.status().is_success() {
            return Ok(None);
        }
        let v: serde_json::Value = resp.json().await?;
        if let Some(price) = v["solana"]["usd"].as_f64() {
            return Ok(Some(price));
        }
        Ok(None)
    }

    async fn fetch_from_binance() -> Result<Option<f64>, reqwest::Error> {
        let resp = reqwest::get(BINANCE_URL).await?;
        if !resp.status().is_success() {
            return Ok(None);
        }
        let v: serde_json::Value = resp.json().await?;
        if let Some(price_str) = v["price"].as_str() {
            if let Ok(price) = price_str.parse::<f64>() {
                return Ok(Some(price));
            }
        }
        Ok(None)
    }

    async fn fetch_from_okx() -> Result<Option<f64>, reqwest::Error> {
        let resp = reqwest::get(OKX_URL).await?;
        if !resp.status().is_success() {
            return Ok(None);
        }
        let v: serde_json::Value = resp.json().await?;
        if let Some(price_str) = v["data"][0]["last"].as_str() {
            if let Ok(price) = price_str.parse::<f64>() {
                return Ok(Some(price));
            }
        }
        Ok(None)
    }
} 