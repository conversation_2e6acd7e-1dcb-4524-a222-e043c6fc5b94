use crate::services::log_sse_manager::{LogEntry, LogSseManagerArc};
use chrono::Utc;
use serde_json::Value;
use tokio::fs::OpenOptions;
use tokio::io::AsyncWriteExt;
use tracing::{Event, Level, Subscriber};
use tracing_subscriber::layer::{Context, Layer};
use tracing_subscriber::registry::LookupSpan;

/// 移除字符串中的ANSI转义码
fn strip_ansi_codes(input: &str) -> String {
    let mut result = String::with_capacity(input.len());
    let mut chars = input.chars();
    
    while let Some(ch) = chars.next() {
        if ch == '\x1b' {
            // 跳过ANSI转义序列
            if chars.next() == Some('[') {
                // 跳过直到找到结束字符 (a-zA-Z)
                while let Some(c) = chars.next() {
                    if c.is_ascii_alphabetic() {
                        break;
                    }
                }
            }
        } else {
            result.push(ch);
        }
    }
    result
}

/// 自定义日志层，同时处理SSE推送和文件写入
pub struct CustomLogLayer {
    /// SSE管理器
    sse_manager: LogSseManagerArc,
    /// 是否启用控制台输出
    enable_console: bool,
    /// 日志文件路径
    log_file_path: String,
}

impl CustomLogLayer {
    /// 创建新的自定义日志层
    pub fn new(sse_manager: LogSseManagerArc, enable_console: bool, log_file_path: String) -> Self {
        Self {
            sse_manager,
            enable_console,
            log_file_path,
        }
    }


}

impl<S> Layer<S> for CustomLogLayer
where
    S: Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    #[inline(always)]
    fn on_event(&self, event: &Event<'_>, _ctx: Context<'_, S>) {
        let metadata = event.metadata();
        let level = metadata.level();
        let target = metadata.target();
        
        // 快速提取消息内容
        let mut message = String::new();
        let mut visitor = MessageVisitor { message: &mut message };
        event.record(&mut visitor);
        
        // 如果没有提取到消息，使用目标名称
        if message.is_empty() {
            message = target.to_string();
        }
        
        // 控制台输出（仅在开发模式，最小延迟）
        if self.enable_console {
            let timestamp_ms = Utc::now().format("%H:%M:%S%.6f").to_string();
            match *level {
                Level::ERROR => eprintln!("🔴 {} {}", timestamp_ms, message),
                Level::WARN => eprintln!("🟡 {} {}", timestamp_ms, message),
                Level::INFO => println!("🟢 {} {}", timestamp_ms, message),
                Level::DEBUG => println!("🔵 {} {}", timestamp_ms, message),
                Level::TRACE => println!("⚪ {} {}", timestamp_ms, message),
            }
        }
        
        // 最小化主线程克隆操作
        let sse_manager = self.sse_manager.clone(); // 轻量级Arc克隆
        let file_path = self.log_file_path.clone();
        let level_str = level.to_string();
        let target_str = target.to_string();
        let message_str = message;
        
        tokio::spawn(async move {
            // 异步生成时间戳（精确到微秒）
            let timestamp = Utc::now().format("%H:%M:%S%.6f").to_string();
            
            // 异步处理SSE推送
            if let Ok(mut manager) = sse_manager.lock() {
                let log_entry = LogEntry {
                    timestamp: timestamp.clone(),
                    level: level_str.clone(),
                    target: target_str.clone(),
                    message: message_str.clone(),
                    fields: Value::Object(serde_json::Map::new()), // 简化字段
                };
                manager.broadcast_log(log_entry);
            }
            
            // 异步文件写入 - 确保每行都有换行符，不包含模块名
            let log_line = format!("{} {} {}\n", timestamp, level_str, message_str);
            
            // 创建logs目录（如果不存在）
            if let Some(parent) = std::path::Path::new(&file_path).parent() {
                let _ = tokio::fs::create_dir_all(parent).await;
            }

            // 异步写入文件
            if let Ok(mut file) = OpenOptions::new()
                .create(true)
                .append(true)
                .open(&file_path)
                .await
            {
                let _ = file.write_all(log_line.as_bytes()).await;
                let _ = file.flush().await;
            }
        });
    }
}

/// 消息访问器，提取日志消息内容
struct MessageVisitor<'a> {
    message: &'a mut String,
}

impl<'a> tracing::field::Visit for MessageVisitor<'a> {
    fn record_debug(&mut self, _field: &tracing::field::Field, value: &dyn std::fmt::Debug) {
        if self.message.is_empty() {
            let formatted = format!("{:?}", value);
            // 移除ANSI转义码
            *self.message = strip_ansi_codes(&formatted);
        }
    }

    fn record_str(&mut self, _field: &tracing::field::Field, value: &str) {
        if self.message.is_empty() {
            *self.message = strip_ansi_codes(value);
        }
    }

    fn record_i64(&mut self, _field: &tracing::field::Field, value: i64) {
        if self.message.is_empty() {
            *self.message = value.to_string();
        }
    }

    fn record_u64(&mut self, _field: &tracing::field::Field, value: u64) {
        if self.message.is_empty() {
            *self.message = value.to_string();
        }
    }

    fn record_f64(&mut self, _field: &tracing::field::Field, value: f64) {
        if self.message.is_empty() {
            *self.message = value.to_string();
        }
    }

    fn record_bool(&mut self, _field: &tracing::field::Field, value: bool) {
        if self.message.is_empty() {
            *self.message = value.to_string();
        }
    }
} 