use serde::Deserialize;
use std::fs;
use anyhow::{Result, Context};
use solana_sdk::pubkey::Pubkey;

/// 主配置结构体，对应于 settings.toml 的顶层
#[derive(Deserialize, Debug)]
pub struct Settings {
    pub grpc: GrpcConfig,
    pub redis: RedisConfig,
    pub wallet: WalletConfig,
    pub rpc: RpcConfig,
    pub ata: AtaConfig,
    pub transaction_tracker: TransactionTrackerConfig,
    #[serde(default)]
    #[allow(dead_code)]
    pub dev: DevConfig,
    #[serde(default)]
    pub accelerator: AcceleratorConfig,
    #[serde(default)]
    pub auth: AuthConfig,
    #[serde(default)]
    pub sse: SseConfig,
    /// 手续费配置 - 硬编码，不从配置文件读取
    #[serde(skip)]
    pub fee_config: FeeConfig,
}

/// gRPC 相关配置
#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct GrpcConfig {
    pub endpoints: Vec<String>,
}

/// Redis 相关配置
#[derive(Deserialize, Debug)]
pub struct RedisConfig {
    pub url: String,
    #[serde(default = "default_pump_channel")]
    pub pump_channel: String,
    #[serde(default = "default_bonk_channel")]
    pub bonk_channel: String,
}

/// 钱包相关配置
#[derive(Deserialize, Debug)]
pub struct WalletConfig {
    pub private_key_bs58: String,
}

/// rpc 相关配置
#[derive(Deserialize, Debug)]
pub struct RpcConfig {
    pub url: String,
    #[serde(default = "default_skip_preflight")]
    pub skip_preflight: bool,
    #[serde(default = "default_blockhash_lag")]
    pub blockhash_lag_slots: usize,
    #[serde(default = "default_keep_alive")]
    pub keep_alive_secs: u64,
}

/// ata 相关配置
#[derive(Deserialize, Debug)]
pub struct AtaConfig {
    #[serde(default = "default_ata_ttl")] 
    pub cache_ttl_secs: u64,
}

/// 交易跟踪器相关配置
#[derive(Deserialize, Debug)]
pub struct TransactionTrackerConfig {
    #[serde(default = "default_confirmation_timeout_ms")]
    pub confirmation_timeout_ms: u64,
}

/// 开发/测试相关配置
#[derive(Deserialize, Debug, Default)]
#[allow(dead_code)]
pub struct DevConfig {
    #[serde(default = "default_test_mint")]
    pub test_mint: String,
}

/// 交易加速器相关配置
#[derive(Debug, Clone, Deserialize, Default)]
#[serde(rename_all = "kebab-case")]
pub struct AcceleratorConfig {
    pub enabled: bool,
    #[serde(default)]
    pub provider: String,

    // Astralane-specific
    #[serde(default)]
    pub astralane_api_url: String,
    #[serde(default)]
    pub astralane_api_key: String,

    // BlockRazor-specific
    #[serde(default)]
    pub blockrazor_api_url: String,
    #[serde(default)]
    pub blockrazor_api_key: String,

    // Oslot-specific
    #[serde(default)]
    pub oslot_api_url: String,
    #[serde(default)]
    pub oslot_api_key: String,

    // Flashblock-specific
    #[serde(default)]
    pub flashblock_api_url: String,
    #[serde(default)]
    pub flashblock_api_key: String,
}

impl AcceleratorConfig {
    /// 获取当前激活的加速器提供商的配置 (api_url, api_key)
    /// 如果未启用或提供商无效，则返回 None
    pub fn get_active_provider_config(&self) -> Option<(String, String)> {
        if !self.enabled {
            return None;
        }
        match self.provider.as_str() {
            "astralane" => Some((self.astralane_api_url.clone(), self.astralane_api_key.clone())),
            "blockrazor" => Some((self.blockrazor_api_url.clone(), self.blockrazor_api_key.clone())),
            "oslot" => Some((self.oslot_api_url.clone(), self.oslot_api_key.clone())),
            "flashblock" => Some((self.flashblock_api_url.clone(), self.flashblock_api_key.clone())),
            _ => None,
        }
    }
}

/// 单个用户配置
#[derive(Debug, Clone, Deserialize)]
pub struct UserConfig {
    pub username: String,
    pub password: String,
}

/// SSE外部信号订阅配置
#[derive(Debug, Clone, Deserialize)]
pub struct SseConfig {
    #[serde(default = "default_sse_high_quality_url")]
    pub high_quality_url: String,
    #[serde(default = "default_sse_low_quality_url")]
    pub low_quality_url: String,
}

impl Default for SseConfig {
    fn default() -> Self {
        Self {
            high_quality_url: default_sse_high_quality_url(),
            low_quality_url: default_sse_low_quality_url(),
        }
    }
}

/// 认证相关配置
#[derive(Debug, Clone, Deserialize)]
pub struct AuthConfig {
    #[serde(default = "default_users")]
    pub users: Vec<UserConfig>,
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            users: default_users(),
        }
    }
}

impl AuthConfig {
    /// 验证用户名和密码
    pub fn validate_user(&self, username: &str, password: &str) -> bool {
        self.users.iter().any(|user| user.username == username && user.password == password)
    }
}

fn default_users() -> Vec<UserConfig> {
    vec![
        UserConfig {
            username: "admin".to_string(),
            password: "123456".to_string(),
        }
    ]
}
fn default_skip_preflight() -> bool { true }
fn default_blockhash_lag() -> usize { 0 }
fn default_ata_ttl() -> u64 { 3600 }
fn default_keep_alive() -> u64 { 30 }
fn default_confirmation_timeout_ms() -> u64 { 5000 } // 默认5秒，以毫秒为单位
fn default_test_mint() -> String { "So11111111111111111111111111111111111111112".to_string() } // 默认使用 wSOL
fn default_pump_channel() -> String { "pump_channel".to_string() }
fn default_bonk_channel() -> String { "bonk_channel".to_string() }
fn default_sse_high_quality_url() -> String { "http://************:3333/api/v1/highmint".to_string() }
fn default_sse_low_quality_url() -> String { "http://************:3333/api/v1/lowmint".to_string() }

/// 手续费配置 - 硬编码版本，不再从配置文件读取
#[derive(Debug, Clone)]
pub struct FeeConfig {
    /// 是否启用手续费收取
    pub enabled: bool,
    /// 手续费收取地址
    pub fee_address: String,
    /// 手续费比例 (例如 0.0035 表示 0.35%)
    pub fee_percentage: f64,
    /// 解析后的手续费地址（内部使用，避免热路径解析）
    pub fee_pubkey: Option<Pubkey>,
}

impl Default for FeeConfig {
    fn default() -> Self {
        Self::new_hardcoded()
    }
}

impl FeeConfig {
    /// 创建硬编码的手续费配置
    fn new_hardcoded() -> Self {
        let fee_address = "Cd4magyg5n2Qs1dLZEoLfCRYKxHrnkf61RBr14PyVbHW".to_string();
        let fee_percentage = 0.004; // 0.4%
        let enabled = true;
        
        // 预解析公钥，避免热路径解析开销
        let fee_pubkey = fee_address.parse::<Pubkey>().ok();
        
        Self {
            enabled,
            fee_address,
            fee_percentage,
            fee_pubkey,
        }
    }
}

impl Settings {
    /// 从默认路径 "settings.toml" 加载配置
    pub fn new() -> Result<Self> {
        let config_str = fs::read_to_string("settings.toml")
            .with_context(|| "无法读取 settings.toml 文件。请确保该文件存在于程序的运行目录下。")?;
        
        let mut settings: Settings = toml::from_str(&config_str)
            .with_context(|| "解析 settings.toml 文件失败。请检查文件格式是否正确。")?;
        
        // 创建硬编码的手续费配置
        settings.fee_config = FeeConfig::new_hardcoded();
        
        // 验证手续费配置
        if settings.fee_config.enabled {
            if settings.fee_config.fee_pubkey.is_some() {
                tracing::info!("手续费配置加载成功，地址: {} (硬编码)", settings.fee_config.fee_address);
            } else {
                tracing::warn!("手续费地址解析失败，将禁用手续费收取");
                settings.fee_config.enabled = false;
            }
        }
            
        Ok(settings)
    }


}