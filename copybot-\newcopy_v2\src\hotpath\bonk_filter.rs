use crate::shared::types::{HotPathTrade, WalletConfig, FollowMode, TradeType};
use crate::hotpath::filter::{FilterConfig, Filter};
use serde_derive::{Deserialize, Serialize};
use std::collections::HashSet;
use tracing::{info, debug};
use chrono::Utc;

/// Bonk专用筛选器结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BonkFilterResult {
    pub is_filtered: bool,
    pub filter_reason: Option<String>,
    pub buy_amount_sol: Option<f64>,
    pub config_used: Option<WalletConfig>,
    pub trade_info: BonkTradeInfo,
}

/// Bonk交易信息摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BonkTradeInfo {
    pub signer: String,
    pub mint: String,
    pub protocol: String,
    pub trade_type: String,
    pub price: f64,
    pub price_before: f64,  // 从Redis提取的交易前价格
    pub price_after: f64,   // 从Redis提取的交易后价格 
    pub slippage: f64,      // 从Redis提取的滑点
    pub actual_trade_price: f64, // 从Redis提取的实际成交价格
    pub sol_cost: f64,
    pub amount: f64,
    pub timestamp: i64,
}

impl From<&HotPathTrade> for BonkTradeInfo {
    fn from(trade: &HotPathTrade) -> Self {
        // 根据交易类型确定描述
        let (trade_type_str, amount_desc) = match trade.trade_type {
            TradeType::Buy => {
                // 买入交易: sol_cost是花费的SOL，token_amount是获得的token
                (format!("{:?}", trade.trade_type), trade.token_amount as f64)
            },
            TradeType::Sell => {
                // 卖出交易: sol_cost是获得的SOL，token_amount是卖出的token
                (format!("{:?}", trade.trade_type), trade.token_amount as f64)
            },
            TradeType::Unknown => {
                (format!("{:?}", trade.trade_type), trade.token_amount as f64)
            }
        };

        // 避免RwLock竞争，直接使用交易数据中的价格信息
        let price_before = 0.0; // 筛选阶段不需要历史价格数据
        let price_after = trade.price;
        let slippage = 0.0; // 筛选阶段不需要滑点数据
        let actual_trade_price = trade.price;

        Self {
            signer: trade.signer.clone(),
            mint: trade.mint_pubkey.to_string(),
            protocol: trade.protocol.clone().unwrap_or("bonk".to_string()),
            trade_type: trade_type_str,
            price: trade.price,
            price_before,
            price_after, 
            slippage,
            actual_trade_price,
            sol_cost: trade.sol_cost,
            amount: amount_desc,
            timestamp: Utc::now().timestamp(),
        }
    }
}

/// Bonk专用筛选器，继承基础筛选器功能
#[derive(Debug, Clone)]
pub struct BonkFilter {
    base_filter: Filter,
    /// Bonk专用配置
    pub bonk_config: BonkFilterConfig,
}

/// Bonk协议专用筛选配置
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct BonkFilterConfig {
    /// 是否启用Bonk筛选
    pub enabled: bool,
    /// Raydium Launchpad专用设置
    pub raydium_launchpad_enabled: bool,
    /// 最小流动性要求（SOL）
    pub min_liquidity_sol: Option<f64>,
    /// 最大流动性要求（SOL）
    pub max_liquidity_sol: Option<f64>,
    /// 交易池黑名单
    pub blocked_pools: HashSet<String>,
    /// 允许的交易池白名单
    pub allowed_pools: HashSet<String>,
    /// Bonk协议特有的最小交易金额
    pub min_trade_amount_sol: Option<f64>,
    /// Bonk协议特有的最大交易金额
    pub max_trade_amount_sol: Option<f64>,
}

impl BonkFilter {
    pub fn new(base_config: FilterConfig, bonk_config: BonkFilterConfig) -> Self {
        Self {
            base_filter: Filter::new(base_config),
            bonk_config,
        }
    }

    /// Bonk专用筛选主入口
    pub fn filter_bonk_trade(&self, trade: &HotPathTrade) -> BonkFilterResult {
        let trade_info = BonkTradeInfo::from(trade);
        
        // 1. 检查Bonk筛选是否启用
        if !self.bonk_config.enabled {
            return BonkFilterResult {
                is_filtered: false,
                filter_reason: Some("Bonk筛选功能未启用".to_string()),
                buy_amount_sol: None,
                config_used: None,
                trade_info,
            };
        }

        // 2. 基础筛选（价格、黑白名单等）
        let wallet_config = match self.base_filter.get_config_if_tracked(trade) {
            Some(config) => config,
            None => {
                return BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some(format!("未找到钱包{}@bonk的跟单配置或配置未激活", trade.signer)),
                    buy_amount_sol: None,
                    config_used: None,
                    trade_info,
                };
            }
        };


        // 3. Bonk协议专用筛选
        if let Some(filter_reason) = self.check_bonk_specific_filters(trade) {
            return BonkFilterResult {
                is_filtered: false,
                filter_reason: Some(filter_reason),
                buy_amount_sol: None,
                config_used: Some(wallet_config.clone()),
                trade_info,
            };
        }

        // 4. 价格筛选检查
        if let Some(min_multiplier) = wallet_config.min_price_multiplier {
            if min_multiplier > 0.0 && trade.price < min_multiplier {
                return BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some(format!("代币价格 {} 低于最小价格限制 {}", 
                                               trade.price, min_multiplier)),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                };
            }
        }

        if let Some(max_multiplier) = wallet_config.max_price_multiplier {
            if max_multiplier > 0.0 && trade.price > max_multiplier {
                return BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some(format!("代币价格 {} 高于最大价格限制 {}", 
                                               trade.price, max_multiplier)),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                };
            }
        }

        // 5. 金额筛选检查
        if let Some(min_sol) = wallet_config.sol_amount_min {
            if trade.sol_cost < min_sol {
                return BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some(format!("交易金额 {} SOL 小于最小金额限制 {} SOL", 
                                               trade.sol_cost, min_sol)),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                };
            }
        }

        if let Some(max_sol) = wallet_config.sol_amount_max {
            if trade.sol_cost > max_sol {
                return BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some(format!("交易金额 {} SOL 大于最大金额限制 {} SOL", 
                                               trade.sol_cost, max_sol)),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                };
            }
        }

        // 6. 只处理买入交易的金额计算
        match trade.trade_type {
            TradeType::Buy => {
                // 计算跟单金额
                let buy_amount_sol = match wallet_config.follow_mode {
                    FollowMode::Percentage => {
                        // 百分比跟单模式 - 基于token数量计算
                        match wallet_config.follow_percentage {
                            Some(pct) => {
                                // 计算目标token数量
                                let target_tokens = trade.token_amount as f64 * (pct / 100.0);
                                // 基于实际交易价格计算所需SOL金额
                                let required_sol = target_tokens * trade.price;
                                required_sol
                            },
                            None => {
                                return BonkFilterResult {
                                    is_filtered: false,
                                    filter_reason: Some("百分比跟单模式未设置跟单比例".to_string()),
                                    buy_amount_sol: None,
                                    config_used: Some(wallet_config.clone()),
                                    trade_info,
                                };
                            }
                        }
                    },
                    FollowMode::FixedAmount => {
                        // 固定金额跟单模式
                        match wallet_config.fixed_follow_amount_sol {
                            Some(amount) => amount,
                            None => {
                                return BonkFilterResult {
                                    is_filtered: false,
                                    filter_reason: Some("固定金额跟单模式未设置跟单金额".to_string()),
                                    buy_amount_sol: None,
                                    config_used: Some(wallet_config.clone()),
                                    trade_info,
                                };
                            }
                        }
                    }
                };

                // 检查计算出的金额是否有效
                if buy_amount_sol <= 0.0 {
                    return BonkFilterResult {
                        is_filtered: false,
                        filter_reason: Some(format!("计算出的跟单金额无效: {} SOL", buy_amount_sol)),
                        buy_amount_sol: None,
                        config_used: Some(wallet_config.clone()),
                        trade_info,
                    };
                }

                // 所有检查通过
                
                BonkFilterResult {
                    is_filtered: true,
                    filter_reason: None,
                    buy_amount_sol: Some(buy_amount_sol),
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                }
            },
            TradeType::Sell => {
                // 卖出交易只记录，不执行跟单  
                BonkFilterResult {
                    is_filtered: true,
                    filter_reason: Some("卖出交易，仅记录不跟单".to_string()),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                }
            },
            TradeType::Unknown => {
                // 未知交易类型，不处理
                BonkFilterResult {
                    is_filtered: false,
                    filter_reason: Some("未知交易类型，跳过处理".to_string()),
                    buy_amount_sol: None,
                    config_used: Some(wallet_config.clone()),
                    trade_info,
                }
            }
        }
    }

    /// Bonk协议专用筛选检查
    fn check_bonk_specific_filters(&self, trade: &HotPathTrade) -> Option<String> {
        // 1. 检查Raydium Launchpad筛选
        if self.bonk_config.raydium_launchpad_enabled {
            // 这里可以添加Raydium Launchpad特有的筛选逻辑
        }

        // 2. 检查流动性筛选
        if let Some(min_liquidity) = self.bonk_config.min_liquidity_sol {
            if trade.sol_cost < min_liquidity {
                return Some(format!("交易金额{}SOL低于最小流动性要求{}SOL", trade.sol_cost, min_liquidity));
            }
        }

        if let Some(max_liquidity) = self.bonk_config.max_liquidity_sol {
            if trade.sol_cost > max_liquidity {
                return Some(format!("交易金额{}SOL超过最大流动性限制{}SOL", trade.sol_cost, max_liquidity));
            }
        }

        // 3. 检查交易池黑名单
        if !self.bonk_config.blocked_pools.is_empty() {
            let mint_str = trade.mint_pubkey.to_string();
            if self.bonk_config.blocked_pools.contains(&mint_str) {
                return Some(format!("代币{}在交易池黑名单中", mint_str));
            }
        }

        // 4. 检查交易池白名单（如果设置了白名单）
        if !self.bonk_config.allowed_pools.is_empty() {
            let mint_str = trade.mint_pubkey.to_string();
            if !self.bonk_config.allowed_pools.contains(&mint_str) {
                return Some(format!("代币{}不在交易池白名单中", mint_str));
            }
        }

        // 5. Bonk协议专用交易金额检查
        if let Some(min_amount) = self.bonk_config.min_trade_amount_sol {
            if trade.sol_cost < min_amount {
                return Some(format!("交易金额{}SOL低于Bonk协议最小交易金额{}SOL", trade.sol_cost, min_amount));
            }
        }

        if let Some(max_amount) = self.bonk_config.max_trade_amount_sol {
            if trade.sol_cost > max_amount {
                return Some(format!("交易金额{}SOL超过Bonk协议最大交易金额{}SOL", trade.sol_cost, max_amount));
            }
        }

        None // 所有检查通过
    }

    /// 打印简化的筛选结果信息
    pub fn log_filter_result(&self, result: &BonkFilterResult) {
        if result.is_filtered {
            // 只在通过筛选时简单记录
            debug!("BONK筛选通过");
        }
    }

    /// 更新Bonk配置
    pub fn update_bonk_config(&mut self, new_config: BonkFilterConfig) {
        self.bonk_config = new_config;
    }

    /// 获取Bonk筛选统计信息
    pub fn get_filter_stats(&self) -> BonkFilterStats {
        BonkFilterStats {
            enabled: self.bonk_config.enabled,
            raydium_launchpad_enabled: self.bonk_config.raydium_launchpad_enabled,
            blocked_pools_count: self.bonk_config.blocked_pools.len(),
            allowed_pools_count: self.bonk_config.allowed_pools.len(),
            has_liquidity_filters: self.bonk_config.min_liquidity_sol.is_some() || self.bonk_config.max_liquidity_sol.is_some(),
            has_amount_filters: self.bonk_config.min_trade_amount_sol.is_some() || self.bonk_config.max_trade_amount_sol.is_some(),
        }
    }
}

/// Bonk筛选器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BonkFilterStats {
    pub enabled: bool,
    pub raydium_launchpad_enabled: bool,
    pub blocked_pools_count: usize,
    pub allowed_pools_count: usize,
    pub has_liquidity_filters: bool,
    pub has_amount_filters: bool,
}