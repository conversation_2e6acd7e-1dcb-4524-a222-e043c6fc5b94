# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
# Rust项目的Cursor索引忽略规则
# 忽略编译产物和目标目录
/target/
/debug/
/release/

# 忽略Cargo锁文件和生成的文档
Cargo.lock

# 忽略大型二进制文件和数据文件
*.bin
*.dat
*.db
*.sqlite
*.sqlite3

# 忽略日志文件
*.log
/logs/

# 忽略环境变量文件但保留示例
# .env  # 已取消忽略.env文件
.env.*
!.env.example
!jito_config_example.env

# 忽略临时文件和缓存
.tmp/
.cache/
.cargo/registry/
.cargo/git/

# 忽略测试覆盖率报告
/coverage/

# 忽略IDE和编辑器特定文件
.vscode/
.idea/
*.iml
*.swp
*.swo

# 忽略大型生成的文件夹
/node_modules/