/// PumpFun程序常量定义
/// 
/// 包含指令判别符、事件判别符等常量

// 指令判别符 (8字节)
pub const INITIALIZE_IX_DISCM: [u8; 8] = [175, 175, 109, 31, 13, 152, 155, 237];
pub const SET_PARAMS_IX_DISCM: [u8; 8] = [27, 234, 178, 52, 147, 2, 187, 141];
pub const CREATE_IX_DISCM: [u8; 8] = [24, 30, 200, 40, 5, 28, 7, 119];
pub const BUY_IX_DISCM: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234];
pub const SELL_IX_DISCM: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173];
pub const WITHDRAW_IX_DISCM: [u8; 8] = [183, 18, 70, 156, 148, 109, 161, 34];

// 事件判别符 (8字节)
pub const CREATE_EVENT_EVENT_DISCM: [u8; 8] = [27, 114, 169, 77, 222, 235, 99, 118];
pub const TRADE_EVENT_EVENT_DISCM: [u8; 8] = [189, 219, 127, 211, 78, 230, 97, 238];
pub const COMPLETE_EVENT_EVENT_DISCM: [u8; 8] = [95, 114, 97, 156, 212, 46, 152, 8];

// 程序相关常量
pub const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
pub const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";
pub const RENT_PROGRAM_ID: &str = "SysvarRent111111111111111111111111111111111";

// 数值常量
pub const LAMPORTS_PER_SOL: u64 = 1_000_000_000;
pub const TOKENS_PER_UNIT: u64 = 1_000_000; // PumpFun代币通常6位小数

// CPI日志前缀
pub const PROGRAM_DATA_PREFIX: &str = "Program data:";
pub const PROGRAM_LOG_PREFIX: &str = "Program log:";

// 账户名称映射
pub const MINT_ACCOUNT_NAME: &str = "mint";
pub const BONDING_CURVE_ACCOUNT_NAME: &str = "bondingCurve";
pub const ASSOCIATED_BONDING_CURVE_ACCOUNT_NAME: &str = "associatedBondingCurve";
pub const ASSOCIATED_USER_ACCOUNT_NAME: &str = "associatedUser";
pub const USER_ACCOUNT_NAME: &str = "user";
pub const SYSTEM_PROGRAM_ACCOUNT_NAME: &str = "systemProgram";
pub const TOKEN_PROGRAM_ACCOUNT_NAME: &str = "tokenProgram";
pub const RENT_ACCOUNT_NAME: &str = "rent";
pub const EVENT_AUTHORITY_ACCOUNT_NAME: &str = "eventAuthority";
pub const PROGRAM_ACCOUNT_NAME: &str = "program";
