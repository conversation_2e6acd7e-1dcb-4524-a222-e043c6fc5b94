# RSI追踪止盈独立开关功能说明

## 🎯 **核心设计理念**

RSI功能作为追踪止盈策略的**独立开关**，与价格回撤条件是**"或"关系**，任何一个条件先触发就先执行卖出，实现更智能的卖出时机控制。

## 📊 **功能架构**

### **1. 数据源 - 真实市场数据**
- **价格数据**：从 `price_receiver: broadcast::Receiver<(Pubkey, f64)>` 获取实时价格
- **交易事件**：从真实交易事件中提取成交量数据 (`TradeEventData`)
- **时间戳**：使用系统时间戳确保数据同步

### **2. RSI计算引擎**
- **多周期支持**：主RSI周期 + 辅助RSI周期组合
- **动态阈值**：基于历史波动率自动调整超买/超卖阈值
- **智能平滑**：支持EMA/SMA/WMA平滑方法，可配置二次平滑

### **3. 卖出决策逻辑**
```
追踪止盈策略 = RSI条件 OR 价格回撤条件

RSI独立检查：
- 基础超买信号 (RSI > 阈值)
- 多周期确认信号
- 拐头检测信号  
- 量价结合分析

价格回撤独立检查：
- 动态/固定回撤百分比
- 基于盈利额度计算触发价

任何一个先触发 → 执行卖出
```

## 🛠️ **核心参数配置**

### **基础RSI参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `rsi_period` | 14 | RSI计算周期，秒级内盘可用5、7、10 |
| `rsi_overbought_threshold` | 70 | 超买阈值，内盘可调至80-90 |
| `rsi_oversold_threshold` | 30 | 超卖阈值，内盘可调至10-20 |

### **智能化增强参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `rsi_dynamic_threshold_enabled` | false | 动态阈值调整开关 |
| `rsi_volatility_window` | 20 | 波动率计算窗口期 |
| `rsi_volatility_adjustment_factor` | 1.0 | 动态调整幅度倍数 |

### **多周期组合参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `rsi_multi_period_enabled` | false | 多周期组合开关 |
| `rsi_secondary_period` | 28 | 第二RSI周期（主周期2倍） |
| `rsi_multi_period_threshold_diff` | 5.0 | 强信号阈值差 |

### **量价结合参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `rsi_volume_price_enabled` | false | 量价结合分析开关 |
| `rsi_volume_surge_multiplier` | 1.5 | 成交量放大倍数阈值 |
| `rsi_high_volume_shrink_weight` | 0.8 | RSI高位+成交量萎缩的卖出权重 |
| `rsi_high_volume_surge_weight` | 0.3 | RSI高位+成交量爆增的观察权重 |

## 🧮 **智能算法细节**

### **1. 动态阈值调整算法**
```rust
if 波动率 > 10% {
    超买阈值 = base_threshold + 调整幅度 * 10.0  // 提高阈值，避免过早卖出
} else if 波动率 < 5% {
    超买阈值 = base_threshold - 调整幅度 * 5.0   // 降低阈值，增加敏感性
}
```

### **2. 量价结合分析**
- **RSI高位 + 成交量萎缩** → 卖出优先级高 (权重0.8)
- **RSI高位 + 成交量爆增** → 警惕加速冲顶，降低卖出冲动 (权重0.3)

### **3. 多周期确认机制**
```
强卖出信号 = RSI主周期 > 超买阈值 AND RSI辅助周期 > (超买阈值 - 阈值差)
```

## 🔄 **工作流程**

### **初始化阶段**
1. 检查策略类型：必须是 `trailing` 追踪止盈策略
2. 检查RSI开关：`rsi_enabled = true`
3. 创建RSI分析器：传入代币地址和配置参数
4. 启动数据收集：价格数据 + 交易事件数据

### **运行时处理**
1. **价格更新**：`rsi_analyzer.update_price(price)`
2. **交易事件**：`rsi_analyzer.add_trade_event(price, volume, is_buy)`
3. **并行检查**：
   - RSI条件检查：`rsi_analyzer.analyze_sell_signal()`
   - 价格回撤检查：`price <= trigger_price`
4. **卖出执行**：任何一个条件触发即执行

### **决策输出**
```rust
RSIAnalysisResult {
    should_sell: bool,           // 是否应该卖出
    sell_signal_strength: f64,   // 信号强度 (0.0-1.0)
    trigger_reason: String,      // 触发原因详细描述
    current_rsi_primary: f64,    // 当前主RSI值
    dynamic_overbought_threshold: f64, // 动态调整后的阈值
    volume_analysis: VolumeAnalysis,   // 成交量分析结果
}
```

## 🎨 **前端界面**

### **配置界面布局**
- **基础配置行**：RSI开关、周期、超买/超卖阈值
- **智能化配置行**：动态阈值开关、波动率窗口、调整因子
- **多周期配置行**：多周期开关、辅助周期、阈值差
- **量价结合配置行**：量价开关、成交量倍数、权重配置

### **用户体验优化**
- **智能提示**：每个参数都有详细的tooltip说明
- **数值约束**：设置合理的min/max/step限制
- **关系说明**：明确标注RSI与价格回撤的"或"关系

## 📈 **应用场景**

### **1. 高频内盘交易**
- **配置建议**：RSI周期5-7，超买阈值80-85
- **优势**：快速响应，减少噪音干扰

### **2. 稳健中期交易**
- **配置建议**：RSI周期14，动态阈值开启
- **优势**：稳定信号，适应市场变化

### **3. 量价分析策略**
- **配置建议**：启用量价结合，设置合理的成交量倍数
- **优势**：结合资金流向，提高决策准确性

## ⚠️ **重要提醒**

1. **独立条件**：RSI和价格回撤是并行的独立条件，不是依赖关系
2. **数据依赖**：RSI计算需要足够的历史数据，建议至少15-20个价格点
3. **参数调优**：不同市场环境需要调整不同的RSI参数
4. **超卖保护**：RSI低于超卖阈值时会阻止卖出，提供保护机制

## 🔧 **技术实现**

### **文件结构**
```
后端:
├── src/shared/types.rs                    # RSI配置字段定义
├── src/services/rsi_analyzer.rs           # RSI计算核心模块
├── src/services/trade_lifecycle_actor.rs  # RSI集成到追踪止盈
└── src/services/mod.rs                    # 模块声明

前端:
├── src/types/api.ts                       # RSI配置类型定义
└── src/components/WalletConfig/StrategyForm.tsx # RSI配置界面
```

### **关键接口**
```rust
// RSI分析器主要接口
impl RSIAnalyzer {
    pub fn new(mint: Pubkey, wallet_config: &WalletConfig) -> Self
    pub fn update_price(&mut self, price: f64)
    pub fn add_trade_event(&mut self, price: f64, volume_sol: f64, is_buy: bool)
    pub fn analyze_sell_signal(&self, current_price: f64, highest_price: f64) -> RSIAnalysisResult
}
```

---

*RSI功能现已完全集成到追踪止盈策略中，提供了强大的技术指标支持，让卖出决策更加智能和精准。*