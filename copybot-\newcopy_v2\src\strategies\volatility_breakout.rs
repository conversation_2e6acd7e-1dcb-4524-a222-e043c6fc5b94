use std::collections::VecDeque;

/// 环形缓冲区，用于高效存储固定大小的数据窗口
#[derive(Debug)]
pub struct RingBuffer<T> {
    buffer: VecDeque<T>,
    capacity: usize,
}

impl<T: Clone> RingBuffer<T> {
    pub fn new(capacity: usize) -> Self {
        Self {
            buffer: VecDeque::with_capacity(capacity),
            capacity,
        }
    }

    #[inline]
    pub fn push(&mut self, item: T) {
        if self.buffer.len() >= self.capacity {
            self.buffer.pop_front();
        }
        self.buffer.push_back(item);
    }

    #[inline]
    pub fn len(&self) -> usize {
        self.buffer.len()
    }

    #[inline]
    pub fn is_empty(&self) -> bool {
        self.buffer.is_empty()
    }

    #[allow(dead_code)]
    pub fn iter(&self) -> impl Iterator<Item = &T> {
        self.buffer.iter()
    }
}

impl RingBuffer<f64> {
    #[inline]
    pub fn sum(&self) -> f64 {
        self.buffer.iter().sum()
    }

    #[inline]
    pub fn mean(&self) -> Option<f64> {
        if self.is_empty() {
            None
        } else {
            Some(self.sum() / self.len() as f64)
        }
    }

    pub fn stddev(&self) -> Option<f64> {
        let mean = self.mean()?;
        let variance = self.buffer.iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / self.len() as f64;
        Some(variance.sqrt())
    }
}

/// 波动率突破检测器
pub struct VolatilityBreakoutDetector {
    // 配置参数
    #[allow(dead_code)]
    bb_window_size: usize,
    bb_stddev: f64,
    #[allow(dead_code)]
    atr_samples: usize,
    #[allow(dead_code)]
    atr_multiplier: f64,
    sell_percent: f64,
    cooldown_ms: u64,
    
    // 数据缓冲区
    price_buffer: RingBuffer<f64>,
    tr_buffer: RingBuffer<f64>,
    
    // 状态
    last_trigger_time: u64,
    last_close: Option<f64>,
    min_price_since_last: f64,
    max_price_since_last: f64,
}

impl VolatilityBreakoutDetector {
    pub fn new(
        bb_window_size: usize,
        bb_stddev: f64,
        atr_samples: usize,
        atr_multiplier: f64,
        sell_percent: f64,
        cooldown_ms: u64,
    ) -> Self {
        Self {
            bb_window_size,
            bb_stddev,
            atr_samples,
            atr_multiplier,
            sell_percent,
            cooldown_ms,
            price_buffer: RingBuffer::new(bb_window_size),
            tr_buffer: RingBuffer::new(atr_samples),
            last_trigger_time: 0,
            last_close: None,
            min_price_since_last: f64::MAX,
            max_price_since_last: f64::MIN,
        }
    }

    /// 处理价格事件，返回是否触发卖出信号和卖出比例
    #[inline]
    pub fn on_price_event(&mut self, price: f64, timestamp_ms: u64) -> Option<(String, f64)> {
        // 检查冷却期
        if timestamp_ms < self.last_trigger_time + self.cooldown_ms {
            return None;
        }

        // 更新价格缓冲区
        self.price_buffer.push(price);

        // 更新ATR
        self.update_atr(price);

        // 检查触发条件
        let signal = self.check_conditions(price);

        // 如果有信号，更新触发时间
        if signal.is_some() {
            self.last_trigger_time = timestamp_ms;
        }

        signal
    }

    #[inline]
    fn update_atr(&mut self, price: f64) {
        // 更新最高最低价
        self.min_price_since_last = self.min_price_since_last.min(price);
        self.max_price_since_last = self.max_price_since_last.max(price);

        if let Some(last_close) = self.last_close {
            // 计算True Range
            let tr1 = self.max_price_since_last - self.min_price_since_last;
            let tr2 = (self.max_price_since_last - last_close).abs();
            let tr3 = (last_close - self.min_price_since_last).abs();
            let true_range = tr1.max(tr2).max(tr3);

            self.tr_buffer.push(true_range);

            // 重置最高最低价
            self.min_price_since_last = price;
            self.max_price_since_last = price;
        }

        self.last_close = Some(price);
    }

    #[inline]
    fn check_conditions(&self, current_price: f64) -> Option<(String, f64)> {
        // 需要足够的样本
        if self.price_buffer.len() < 10 || self.tr_buffer.len() < 5 {
            return None;
        }

        // 检查布林带突破
        if let (Some(mean), Some(stddev)) = (self.price_buffer.mean(), self.price_buffer.stddev()) {
            let upper_band = mean + self.bb_stddev * stddev;
            
            if current_price > upper_band {
                return Some((
                    format!(
                        "波动率突破(布林带): 价格 {:.6} > 上轨 {:.6}",
                        current_price, upper_band
                    ),
                    self.sell_percent
                ));
            }
        }

        // 检查ATR波动率突增
        if !self.tr_buffer.is_empty() {
            let raw_avg_tr = self.tr_buffer.sum() / self.tr_buffer.len() as f64;
            let avg_tr = raw_avg_tr.max(1e-9);
            
            if let Some(&current_tr) = self.tr_buffer.buffer.back() {
                // 只输出相对放大百分比，避免打印极小数值
                let pct = (current_tr / avg_tr - 1.0) * 100.0;
                let signal = Some((
                    format!("波动率突破(ATR): +{:.2}%", pct),
                    self.sell_percent
                ));
                return signal;
            }
        }

        None
    }

    /// 获取当前时间戳（毫秒）
    pub fn get_current_timestamp_ms() -> u64 {
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64
    }
} 