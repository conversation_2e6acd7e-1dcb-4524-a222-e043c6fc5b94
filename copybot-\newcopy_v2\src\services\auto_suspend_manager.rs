use crate::shared::types::{WalletConfig, AutoSuspendConfig};
use crate::hotpath::filter::FilterCommand;
use crate::shared::constants::FILTER_UPDATE_CHANNEL;
use std::collections::{HashMap, VecDeque};
use std::sync::RwLock;
use tracing::{warn, error};
use serde_json;
use redis::{self, AsyncCommands};

/// 简单的自动暂停管理器
pub struct AutoSuspendManager {
    /// 钱包地址 -> 最近交易盈亏列表
    histories: RwLock<HashMap<String, VecDeque<f64>>>,
    /// Redis 客户端
    redis_client: redis::Client,
}

impl AutoSuspendManager {
    pub fn new(redis_client: redis::Client) -> Self {
        Self {
            histories: RwLock::new(HashMap::new()),
            redis_client,
        }
    }

    /// 记录交易完成（冷路径）
    pub async fn record_trade_completion(
        &self,
        followed_wallet: &str,
        profit_percentage: f64,
        wallet_config: &WalletConfig,
    ) {
        // 1. 检查是否启用
        let config = match &wallet_config.auto_suspend_config {
            Some(c) if c.enabled => c,
            _ => return,
        };

        // 2. 记录交易
        {
            let mut histories = self.histories.write().unwrap();
            let history = histories.entry(followed_wallet.to_string())
                .or_insert_with(|| VecDeque::new());
            
            history.push_back(profit_percentage);
            while history.len() > config.window_size {
                history.pop_front();
            }
        }

        // 3. 检查是否需要暂停
        if self.should_suspend(followed_wallet, config) {
            self.send_suspend_command(wallet_config).await;
        }
    }

    /// 检查是否应该暂停
    fn should_suspend(&self, wallet: &str, config: &AutoSuspendConfig) -> bool {
        let histories = self.histories.read().unwrap();
        let history = match histories.get(wallet) {
            Some(h) => h,
            None => return false,
        };

        // 只有在有足够的交易记录时才检查（至少要有loss_count笔交易）
        if history.len() < config.loss_count {
            return false;
        }

        // 统计最近window_size笔交易中的亏损笔数
        let recent_trades = if history.len() > config.window_size {
            &history.as_slices().0[history.len() - config.window_size..]
        } else {
            history.as_slices().0
        };

        let loss_count = recent_trades.iter()
            .filter(|&&profit| profit < config.loss_threshold)
            .count();

        loss_count >= config.loss_count
    }

    /// 发送暂停命令
    async fn send_suspend_command(&self, wallet_config: &WalletConfig) {
        let mut updated_config = wallet_config.clone();
        updated_config.is_active = false;

        let command = FilterCommand::UpdateWallet(updated_config);
        let command_json = match serde_json::to_string(&command) {
            Ok(json) => json,
            Err(e) => {
                error!("序列化暂停命令失败: {}", e);
                return;
            }
        };

        match self.redis_client.get_multiplexed_async_connection().await {
            Ok(mut conn) => {
                let result: Result<i32, redis::RedisError> = conn.publish(FILTER_UPDATE_CHANNEL, &command_json).await;
                match result {
                    Ok(_) => {
                        warn!("🚨 自动暂停钱包: {} (连续亏损)", &wallet_config.wallet_address[..8]);
                    }
                    Err(e) => {
                        error!("发送暂停命令到Redis失败: {}", e);
                    }
                }
            }
            Err(e) => {
                error!("获取Redis连接失败: {}", e);
            }
        }
    }

    /// 获取钱包统计信息（用于调试）
    pub fn get_wallet_stats(&self, wallet_address: &str) -> Option<(usize, usize, f64)> {
        let histories = self.histories.read().unwrap();
        if let Some(history) = histories.get(wallet_address) {
            let total_trades = history.len();
            let loss_trades = history.iter().filter(|&&p| p < 0.0).count();
            let avg_profit: f64 = if total_trades > 0 {
                history.iter().sum::<f64>() / total_trades as f64
            } else {
                0.0
            };
            
            Some((total_trades, loss_trades, avg_profit))
        } else {
            None
        }
    }
}
