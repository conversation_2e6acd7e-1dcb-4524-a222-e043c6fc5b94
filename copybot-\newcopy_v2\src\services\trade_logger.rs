use crate::shared::types::TradeRecord;
use tokio::{
    fs::OpenOptions,
    io::AsyncWriteExt,
    sync::mpsc,
};
use tracing::{error, info, warn};

const TRADE_HISTORY_FILE: &str = "trade_history.jsonl";

/// 异步交易记录器服务
pub struct TradeLogger {
    receiver: mpsc::Receiver<TradeRecord>,
}

impl TradeLogger {
    /// 创建一个新的 TradeLogger 实例及其通信通道的发送端
    pub fn new() -> (Self, mpsc::Sender<TradeRecord>) {
        // 创建一个容量为100的通道，以应对突发流量
        let (sender, receiver) = mpsc::channel(100);
        (Self { receiver }, sender)
    }

    /// 启动日志记录器的主循环。
    /// 此方法会持续运行，直到通道被关闭。
    pub async fn start(mut self) {
        info!("交易历史记录器服务已启动，将记录到 {}", TRADE_HISTORY_FILE);
        while let Some(record) = self.receiver.recv().await {
            if let Err(e) = self.log_to_file(&record).await {
                error!("写入交易历史记录失败: {}", e);
            }
        }
        warn!("交易历史记录器通道已关闭，服务正在停止。");
    }

    /// 将单条交易记录异步写入到文件
    async fn log_to_file(&self, record: &TradeRecord) -> anyhow::Result<()> {
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(TRADE_HISTORY_FILE)
            .await?;

        let mut json_string = serde_json::to_string(record)?;
        json_string.push('\n'); // 添加换行符，以符合jsonl格式

        file.write_all(json_string.as_bytes()).await?;
        
        Ok(())
    }
} 