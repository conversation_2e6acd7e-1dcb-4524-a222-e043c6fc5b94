use axum::response::sse::Event;
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::convert::Infallible;
use std::sync::{Arc, Mutex};
use tokio::sync::mpsc;


/// 日志条目结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: String,
    pub level: String,
    pub target: String,
    pub message: String,
    pub fields: serde_json::Value,
}

/// 日志SSE管理器
pub struct LogSseManager {
    /// 注册的客户端发送通道
    clients: Vec<mpsc::Sender<Result<Event, Infallible>>>,
    /// 最近的日志缓存（用于新连接的客户端）
    recent_logs: VecDeque<LogEntry>,
    /// 最大缓存日志数量
    max_cache_size: usize,
}

impl LogSseManager {
    /// 创建新的日志SSE管理器
    pub fn new(max_cache_size: usize) -> Self {
        Self {
            clients: Vec::new(),
            recent_logs: VecDeque::new(),
            max_cache_size,
        }
    }

    /// 注册新的SSE客户端
    pub fn register(&mut self, sender: mpsc::Sender<Result<Event, Infallible>>) {
        // 发送最近的日志给新客户端
        for log_entry in &self.recent_logs {
            if let Ok(json) = serde_json::to_string(log_entry) {
                let event = Event::default()
                    .event("log")
                    .data(json);
                
                // 忽略发送失败（客户端可能已断开）
                let _ = sender.try_send(Ok(event));
            }
        }

        self.clients.push(sender);
    }

    /// 广播日志条目到所有客户端（优化版本，减少序列化开销）
    pub fn broadcast_log(&mut self, log_entry: LogEntry) {
        // 如果没有客户端，直接跳过序列化
        if self.clients.is_empty() {
            // 仍然需要缓存
            self.recent_logs.push_back(log_entry);
            if self.recent_logs.len() > self.max_cache_size {
                self.recent_logs.pop_front();
            }
            return;
        }

        // 预先序列化一次，避免重复序列化
        let json = match serde_json::to_string(&log_entry) {
            Ok(json) => json,
            Err(_) => return, // 序列化失败，跳过此条日志
        };

        let event = Event::default()
            .event("log")
            .data(json);

        // 广播到所有客户端，移除失败的连接
        self.clients.retain(|client| {
            client.try_send(Ok(event.clone())).is_ok()
        });

        // 添加到缓存（在广播后，避免不必要的clone）
        self.recent_logs.push_back(log_entry);
        if self.recent_logs.len() > self.max_cache_size {
            self.recent_logs.pop_front();
        }
    }

    /// 清除日志缓存
    pub fn clear_logs(&mut self) {
        self.recent_logs.clear();
        
        // 发送清除事件到所有客户端
        let event = Event::default()
            .event("clear")
            .data("logs_cleared");
            
        self.clients.retain(|client| {
            client.try_send(Ok(event.clone())).is_ok()
        });
    }

    /// 获取当前连接的客户端数量
    pub fn client_count(&self) -> usize {
        self.clients.len()
    }

    /// 获取缓存的日志数量
    pub fn cached_log_count(&self) -> usize {
        self.recent_logs.len()
    }
}

/// 全局日志SSE管理器实例
pub type LogSseManagerArc = Arc<Mutex<LogSseManager>>;

/// 创建全局日志SSE管理器
pub fn create_log_sse_manager() -> LogSseManagerArc {
    Arc::new(Mutex::new(LogSseManager::new(1000))) // 缓存最近1000条日志
} 