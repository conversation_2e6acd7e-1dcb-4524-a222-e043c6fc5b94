import React, { createContext, useContext, useCallback, useState, useRef, useEffect } from 'react';
import { message } from 'antd';
import ApiService from '../services/api';

// 代币价格监控状态类型
interface TokenPriceState {
  tokenAddress: string;
  tokenPrice: number; // 美元价格
  tokenSolPrice: number; // 币本位价格(SOL)
  tokenSymbol: string;
  priceLoading: boolean;
  initialPrice: number; // 监控开始时的初始价格
  priceChangePercent: number; // 涨跌百分比
  isMonitoring: boolean; // 是否正在监控
}

// Context类型
interface TokenPriceMonitorContextType {
  // 状态
  state: TokenPriceState;
  
  // 操作方法
  startMonitoring: (tokenAddress: string) => Promise<void>;
  stopMonitoring: () => void;
  resetMonitoringState: () => void;
  formatPrice: (price: number, prefix?: string) => string;
}

// 初始状态
const initialState: TokenPriceState = {
  tokenAddress: '',
  tokenPrice: 0,
  tokenSolPrice: 0,
  tokenSymbol: 'TOKEN',
  priceLoading: false,
  initialPrice: 0,
  priceChangePercent: 0,
  isMonitoring: false,
};

// 创建Context
const TokenPriceMonitorContext = createContext<TokenPriceMonitorContextType | null>(null);

// Provider组件
export const TokenPriceMonitorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<TokenPriceState>(initialState);
  const priceEventSourceRef = useRef<EventSource | null>(null);

  // 获取下标数字
  const getSubscript = useCallback((num: number): string => {
    const subscripts = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉'];
    return num.toString().split('').map(digit => subscripts[parseInt(digit)]).join('');
  }, []);

  // 统一价格格式化函数
  const formatPrice = useCallback((price: number, prefix: string = '$'): string => {
    if (price === 0) return `${prefix}0.00`;
    
    if (price >= 1) {
      return `${prefix}${price.toFixed(2)}`;
    }
    
    // 转换为固定小数点格式
    const fixedStr = price.toFixed(20);
    const decimalIndex = fixedStr.indexOf('.');
    
    if (decimalIndex === -1) {
      return `${prefix}${price.toFixed(6)}`;
    }
    
    // 计算小数点后连续0的个数
    let zeroCount = 0;
    for (let i = decimalIndex + 1; i < fixedStr.length; i++) {
      if (fixedStr[i] === '0') {
        zeroCount++;
      } else {
        break;
      }
    }
    
    if (zeroCount >= 4) {
      // 使用下标格式，根据实际零的个数显示正确的下标数字
      const significantStart = decimalIndex + 1 + zeroCount;
      const significantDigits = fixedStr.substring(significantStart, significantStart + 5);
      return `${prefix}0.0${getSubscript(zeroCount)}${significantDigits}`;
    } else {
      return `${prefix}${price.toFixed(8)}`;
    }
  }, [getSubscript]);

  // 启动监控
  const startMonitoring = useCallback(async (tokenAddress: string) => {
    if (!tokenAddress.trim()) {
      message.warning('请输入代币合约地址');
      return;
    }

    if (state.isMonitoring) {
      // 如果已在监控，先停止
      stopMonitoring();
    }

    setState(prev => ({ ...prev, priceLoading: true, tokenAddress }));

    try {
      console.log('🔍 开始监控代币价格:', tokenAddress);
      
      // 建立SSE连接到价格流端点
      const eventSource = new EventSource('/api/v1/prices/stream');
      priceEventSourceRef.current = eventSource;
      
      eventSource.onopen = () => {
        console.log('✅ 价格SSE连接已建立');
        setState(prev => ({ ...prev, isMonitoring: true, priceLoading: false }));
        message.success('开始监控代币价格');
      };

      eventSource.onerror = (error) => {
        console.error('❌ 价格SSE连接错误:', error);
        setState(prev => ({ ...prev, isMonitoring: false, priceLoading: false }));
        message.error('价格监控连接失败');
        eventSource.close();
        priceEventSourceRef.current = null;
      };

      eventSource.onmessage = (event) => {
        try {
          if (event.data.trim() === '' || event.data.includes('keep-alive')) {
            return; // 跳过心跳消息
          }

          const priceData = JSON.parse(event.data);
          console.log('📊 收到价格数据:', priceData);
          
          // 检查是否是我们监控的代币
          if (priceData.mint === tokenAddress) {
            const solPrice_current = priceData.price; // 这是币本位价格 (以SOL计价)
            const solPrice = ApiService.getCachedSolPrice();
            
            // 计算美元价格 = 币本位价格 × SOL美元价格
            const usdPrice = solPrice_current * solPrice;
            
            setState(prev => {
              // 使用函数式更新来避免闭包陷阱
              const newInitialPrice = prev.initialPrice === 0 ? usdPrice : prev.initialPrice;
              const changePercent = prev.initialPrice === 0 ? 0 : ((usdPrice - prev.initialPrice) / prev.initialPrice) * 100;
              
              return {
                ...prev,
                tokenPrice: usdPrice,
                tokenSolPrice: solPrice_current,
                tokenSymbol: 'TOKEN',
                initialPrice: newInitialPrice,
                priceChangePercent: changePercent,
              };
            });
          }
        } catch (error) {
          console.error('❌ 解析价格数据失败:', error, event.data);
        }
      };

    } catch (error) {
      console.error('启动价格监控失败:', error);
      message.error('启动价格监控失败');
      setState(prev => ({ ...prev, priceLoading: false }));
    }
  }, [state.isMonitoring]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (priceEventSourceRef.current) {
      priceEventSourceRef.current.close();
      priceEventSourceRef.current = null;
    }
    setState(prev => ({ ...prev, isMonitoring: false }));
    message.info('已停止价格监控');
  }, []);

  // 重置监控状态
  const resetMonitoringState = useCallback(() => {
    if (priceEventSourceRef.current) {
      priceEventSourceRef.current.close();
      priceEventSourceRef.current = null;
    }
    setState(initialState);
  }, []);

  // 组件卸载时清理SSE连接
  useEffect(() => {
    return () => {
      if (priceEventSourceRef.current) {
        console.log('TokenPriceMonitorProvider卸载，关闭价格SSE连接');
        priceEventSourceRef.current.close();
      }
    };
  }, []);

  const contextValue: TokenPriceMonitorContextType = {
    state,
    startMonitoring,
    stopMonitoring,
    resetMonitoringState,
    formatPrice,
  };

  return (
    <TokenPriceMonitorContext.Provider value={contextValue}>
      {children}
    </TokenPriceMonitorContext.Provider>
  );
};

// Hook来使用Context
export const useTokenPriceMonitor = () => {
  const context = useContext(TokenPriceMonitorContext);
  if (!context) {
    throw new Error('useTokenPriceMonitor must be used within TokenPriceMonitorProvider');
  }
  return context;
};