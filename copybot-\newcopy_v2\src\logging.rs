use crate::services::custom_log_layer::CustomLogLayer;
use crate::services::log_sse_manager::{create_log_sse_manager, LogSseManagerArc};
use std::env;
use tracing::Level;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter, Registry};

/// 初始化日志系统
/// 
/// # Arguments
/// * `is_production` - 是否为生产模式
/// 
/// # Returns
/// 返回日志SSE管理器的Arc引用
pub fn init_logging(is_production: bool) -> LogSseManagerArc {
    // 创建日志SSE管理器
    let log_sse_manager = create_log_sse_manager();
    
    // 确定日志级别
    let _log_level = if is_production {
        Level::INFO
    } else {
        Level::DEBUG
    };
    
    // 创建环境过滤器（生产模式下过滤更多日志）
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| {
            if is_production {
                // 生产模式：只显示重要日志，过滤掉外部库的日志
                EnvFilter::new("warn,newcopy_v2=info,solana_client=error,solana_transaction_status=error,solana_sdk=error,yellowstone_grpc_client=error,tonic=error,reqwest=error")
            } else {
                // 开发模式：更详细的日志
                EnvFilter::new("debug,solana_client=warn,solana_transaction_status=warn,solana_sdk=warn,yellowstone_grpc_client=warn,tonic=warn,reqwest=warn")
            }
        });
    
    // 生成日志文件路径（按日期）
    let log_file_path = format!(
        "logs/app_{}.log",
        chrono::Utc::now().format("%Y%m%d")
    );
    
    // 创建自定义日志层
    let custom_layer = CustomLogLayer::new(
        log_sse_manager.clone(),
        !is_production, // 只在非生产模式下启用控制台输出
        log_file_path.clone(),
    );
    
    // 配置tracing subscriber
    Registry::default()
        .with(env_filter)
        .with(custom_layer)
        .init();
    
    // 根据模式打印初始化信息
    if is_production {
        tracing::info!("🚀 生产模式启动 - 日志输出到文件和SSE，控制台输出已禁用");
    } else {
        tracing::info!("🔧 开发模式启动 - 日志输出到控制台、文件和SSE");
    }
    
    tracing::info!("📝 日志文件路径: {}", log_file_path);
    tracing::info!("📡 日志SSE端点: /api/v1/logs/stream");
    tracing::info!("🗑️ 日志清除端点: POST /api/v1/logs/clear");
    
    log_sse_manager
}

/// 检测是否为生产模式
pub fn is_production_mode() -> bool {
    // 检查环境变量
    match env::var("RUST_ENV").as_deref() {
        Ok("production") | Ok("prod") => true,
        _ => {
            // 检查是否为release构建
            cfg!(not(debug_assertions))
        }
    }
}

/// 获取日志配置信息
pub fn get_log_info() -> serde_json::Value {
    let is_prod = is_production_mode();
    serde_json::json!({
        "mode": if is_prod { "production" } else { "development" },
        "console_output": !is_prod,
        "file_output": true,
        "sse_output": true,
        "log_file": format!("logs/app_{}.log", chrono::Utc::now().format("%Y%m%d")),
        "endpoints": {
            "stream": "/api/v1/logs/stream",
            "clear": "POST /api/v1/logs/clear",
            "files": "GET /api/v1/logs"
        }
    })
} 