import React, { useState } from 'react';
import { Form, Row, Col, Select, InputNumber, Typography, Switch, Button, Card, Divider, Alert } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { DynamicTrailingLevel } from '../../types/api';

const { Option } = Select;

interface StrategyFormProps {
  form: any;
}

const StrategyForm: React.FC<StrategyFormProps> = ({ form }) => {
  const strategy = Form.useWatch('take_profit_strategy', form);

  return (
    <>
      <Typography.Title level={5} style={{ margin: '0 0 8px 0', color: '#13c2c2' }}>
        📈 止盈策略配置
      </Typography.Title>
      
      <Row gutter={[8, 4]}>
        <Col span={4}>
          <Form.Item
            name="take_profit_strategy"
            label="止盈策略类型"
            rules={[{ required: true, message: '请选择止盈策略' }]}
          >
            <Select placeholder="选择策略">
              <Option value="standard">标准分步止盈</Option>
              <Option value="trailing">动态追踪止盈</Option>
              <Option value="exponential">指数递增止盈</Option>
              <Option value="volatility">布林带策略</Option>
            </Select>
          </Form.Item>
        </Col>

        {/* 标准止盈策略 */}
        {strategy === 'standard' && (
          <>
            <Col span={4}>
              <Form.Item name="take_profit_start_pct" label="起始止盈阈值 (%)">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="20"
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="take_profit_step_pct" label="止盈步长 (%)">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="10"
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="take_profit_sell_portion_pct" label="每次卖出比例 (%)">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="25"
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
          </>
        )}

        {/* 增强追踪止盈策略 */}
        {strategy === 'trailing' && (
          <TrailingStrategyForm form={form} />
        )}

        {/* 指数止盈策略 */}
        {strategy === 'exponential' && (
          <>
            <Col span={4}>
              <Form.Item name="exponential_sell_trigger_step_pct" label="触发步长 (%)">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="10"
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="exponential_sell_base_portion_pct" label="基础卖出比例 (%)">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="20"
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="exponential_sell_power" label="指数幂">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="2.0"
                  step={0.01}
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
          </>
        )}

        {/* Volatility Strategy Fields */}
        {strategy === 'volatility' && (
          <>
            <Col span={4}>
              <Form.Item
                label="布林带窗口大小"
                name="volatility_bb_window_size"
                rules={[{ required: true, message: '请输入布林带窗口大小' }]}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  min={100} 
                  max={10000} 
                  placeholder="1000" 
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="布林带标准差倍数"
                name="volatility_bb_stddev"
                rules={[{ required: true, message: '请输入布林带标准差倍数' }]}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  min={0.5} 
                  max={5} 
                  step={0.1} 
                  placeholder="1.8" 
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="ATR样本数"
                name="volatility_atr_samples"
                rules={[{ required: true, message: '请输入ATR样本数' }]}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  min={10} 
                  max={1000} 
                  placeholder="100" 
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="ATR突增阈值倍数"
                name="volatility_atr_multiplier"
                rules={[{ required: true, message: '请输入ATR突增阈值倍数' }]}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  min={0.5} 
                  max={5} 
                  step={0.1} 
                  placeholder="1.3" 
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="波动率策略卖出比例"
                name="volatility_sell_percent"
                rules={[{ required: true, message: '请输入波动率策略卖出比例' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={100}
                  formatter={(value) => `${value}%`}
                  parser={(value) => value?.replace('%', '') as any}
                  placeholder="40"
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="波动率策略冷却时间"
                name="volatility_cooldown_ms"
                rules={[{ required: true, message: '请输入波动率策略冷却时间' }]}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  min={0} 
                  max={10000} 
                  placeholder="500" 
                  addonAfter="ms" 
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                label="最小卖出保护比例"
                name="min_partial_sell_pct"
                tooltip="当剩余仓位占初始仓位比例低于该值时直接清仓"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={(value) => `${value}%`}
                  parser={(value) => value?.replace('%', '') as any}
                  placeholder="30"
                />
              </Form.Item>
            </Col>
          </>
        )}
      </Row>
    </>
  );
};

// 增强追踪止盈策略配置组件
const TrailingStrategyForm: React.FC<StrategyFormProps> = ({ form }) => {
  const [useDynamicLevels, setUseDynamicLevels] = useState(false);
  const [dynamicLevels, setDynamicLevels] = useState<DynamicTrailingLevel[]>([]);

  // 监听表单变化，解析dynamic_trailing_levels
  React.useEffect(() => {
    const dynamicLevelsStr = form.getFieldValue('dynamic_trailing_levels');
    if (dynamicLevelsStr) {
      try {
        const levels = JSON.parse(dynamicLevelsStr);
        if (Array.isArray(levels) && levels.length > 0) {
          setDynamicLevels(levels);
          setUseDynamicLevels(true);
        }
      } catch (e) {
        console.warn('解析动态追踪级别失败:', e);
      }
    }
  }, [form]);

  // 更新动态级别配置
  const updateDynamicLevels = (levels: DynamicTrailingLevel[]) => {
    // 不再自动排序，保持用户输入的顺序
    setDynamicLevels(levels);
    // 在动态模式下，总是同步数据到表单，让后端和提交逻辑处理有效性
    if (useDynamicLevels) {
      if (levels.length > 0) {
        const jsonValue = JSON.stringify(levels);
        console.log('🔵 [DEBUG] 设置动态级别到表单:', jsonValue);
        form.setFieldValue('dynamic_trailing_levels', jsonValue);
      } else {
        console.log('🔵 [DEBUG] 动态模式但无级别，设置为null');
        form.setFieldValue('dynamic_trailing_levels', null);
      }
    } else {
      console.log('🔵 [DEBUG] 固定模式，清空动态级别');
      form.setFieldValue('dynamic_trailing_levels', null);
    }
  };

  // 切换动态/固定模式
  const handleModeChange = (checked: boolean) => {
    console.log('🔵 [DEBUG] 模式切换:', checked ? '动态回撤' : '固定回撤');
    setUseDynamicLevels(checked);
    if (checked) {
      // 启用动态模式，清除固定模式字段
      form.setFieldValue('trailing_stop_profit_percentage', null);
      // 直接设置表单值，避免状态时序问题
      if (dynamicLevels.length > 0) {
        const jsonValue = JSON.stringify(dynamicLevels);
        console.log('🔵 [DEBUG] 切换到动态模式，已有级别:', jsonValue);
        form.setFieldValue('dynamic_trailing_levels', jsonValue);
      } else {
        console.log('🔵 [DEBUG] 切换到动态模式，无级别数据');
        form.setFieldValue('dynamic_trailing_levels', null);
      }
    } else {
      // 启用固定模式，清除动态模式字段
      console.log('🔵 [DEBUG] 切换到固定模式');
      form.setFieldValue('dynamic_trailing_levels', null);
    }
  };

  // 添加新级别
  const addLevel = () => {
    if (dynamicLevels.length >= 10) return;
    
    // 根据现有级别智能推荐默认值
    let newLevel: DynamicTrailingLevel;
    if (dynamicLevels.length === 0) {
      // 第一个级别：给一个实用的默认值，用户可以修改
      newLevel = { profit_pct: 10, trailing_pct: 5 };
    } else {
      // 后续级别：基于上一级别智能推荐
      const lastLevel = dynamicLevels[dynamicLevels.length - 1];
      newLevel = {
        profit_pct: lastLevel.profit_pct + 10, // 比上一级别高10%
        trailing_pct: Math.min(lastLevel.trailing_pct + 2, 15) // 回撤增加2%，最高15%
      };
    }
    
    console.log('🔵 [DEBUG] 添加新级别:', newLevel);
    const newLevels = [...dynamicLevels, newLevel];
    console.log('🔵 [DEBUG] 更新后的级别数组:', newLevels);
    updateDynamicLevels(newLevels);
  };

  // 删除级别
  const removeLevel = (index: number) => {
    if (dynamicLevels.length <= 1) return;
    const newLevels = dynamicLevels.filter((_, i) => i !== index);
    updateDynamicLevels(newLevels);
  };

  // 更新级别
  const updateLevel = (index: number, field: keyof DynamicTrailingLevel, value: number) => {
    // 基本验证 - 在更新之前进行
    if (field === 'profit_pct' && value < 0) return;
    if (field === 'trailing_pct' && value < 0) return; // 允许0值，用户可能想要先输入0再修改
    if (field === 'trailing_pct' && value >= 100) return; // 不允许100%或更高
    
    const newLevels = [...dynamicLevels];
    newLevels[index] = { ...newLevels[index], [field]: value };
    
    updateDynamicLevels(newLevels);
  };

  return (
    <>
      {/* 隐藏的Form.Item用于存储dynamic_trailing_levels字段 */}
      <Form.Item name="dynamic_trailing_levels" style={{ display: 'none' }}>
        <input type="hidden" />
      </Form.Item>
      
      <Col span={24}>
        <Card size="small" title="🎯 增强追踪止盈策略配置">
          <Row gutter={16}>
            <Col span={24}>
              <Alert
                message="策略说明"
                description="启用追踪止盈策略后将自动禁用回调止损，避免策略冲突。支持动态回撤级别和回调暂缓机制。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="回撤模式" style={{ marginBottom: 16 }}>
                <Switch
                  checked={useDynamicLevels}
                  onChange={handleModeChange}
                  checkedChildren="动态回撤"
                  unCheckedChildren="固定回撤"
                />
              </Form.Item>
            </Col>
          </Row>

          {!useDynamicLevels ? (
            // 固定回撤模式
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item name="trailing_stop_profit_percentage" label="固定回撤百分比 (%)">
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="15"
                    min={0.1}
                    max={99.9}
                    step={0.1}
                    stringMode
                    controls={false}
                  />
                </Form.Item>
              </Col>
            </Row>
          ) : (
            // 动态回撤模式
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Typography.Text strong>动态回撤级别配置 (最多10个)</Typography.Text>
                <Typography.Text type="secondary" style={{ marginLeft: 8 }}>
                  根据不同盈利区间设置不同的回撤百分比
                </Typography.Text>
              </div>
              
              {dynamicLevels.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '20px', background: '#f5f5f5', borderRadius: '6px', marginBottom: 16 }}>
                  <Typography.Text type="secondary">暂无级别配置，请点击下方按钮添加</Typography.Text>
                </div>
              ) : (
                dynamicLevels.map((level, index) => (
                <Row key={index} gutter={16} style={{ marginBottom: 8 }}>
                  <Col span={4}>
                    <InputNumber
                      placeholder="盈利%"
                      value={level.profit_pct}
                      onChange={(value) => updateLevel(index, 'profit_pct', value || 0)}
                      step={1}
                      style={{ width: '100%' }}
                      addonAfter="%盈利时"
                    />
                  </Col>
                  <Col span={4}>
                    <InputNumber
                      placeholder="回撤%"
                      value={level.trailing_pct}
                      onChange={(value) => updateLevel(index, 'trailing_pct', value || 0)}
                      min={0.1}
                      max={99.9}
                      step={0.1}
                      style={{ width: '100%' }}
                      addonAfter="%回撤"
                    />
                  </Col>
                  <Col span={2}>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeLevel(index)}
                      disabled={dynamicLevels.length <= 1}
                    />
                  </Col>
                </Row>
                ))
              )}
              
              {dynamicLevels.length < 10 && (
                <Button
                  type="dashed"
                  onClick={addLevel}
                  icon={<PlusOutlined />}
                  style={{ width: '200px' }}
                >
                  添加级别
                </Button>
              )}
            </div>
          )}

          <Divider orientation="left" orientationMargin="0">回调暂缓机制</Divider>
          
          <Row gutter={16}>
            <Col span={4}>
              <Form.Item name="enable_backfill_delay" label="启用回调暂缓">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item 
                name="backfill_delay_ms" 
                label="等待时间 (毫秒)"
                tooltip="达到回调触发点后等待的时间"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="3000"
                  step={500}
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item 
                name="backfill_rebound_threshold_pct" 
                label="反弹阈值 (%)"
                tooltip="从触发点反弹多少百分比才能取消本次回调"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="2.0"
                  step={0.1}
                  stringMode
                  controls={false}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Alert
                message="回调暂缓说明"
                description="启用后，当达到回调触发点时不会立即清仓，而是等待指定时间观察价格反弹。如果反弹达到阈值则取消本次触发，否则延迟执行清仓。"
                type="warning"
                showIcon
                style={{ marginTop: 8 }}
              />
            </Col>
          </Row>
        </Card>
      </Col>
    </>
  );
};

export default StrategyForm; 