import React from 'react';
import { Form, Row, Col, Radio, Input, Typography, Switch } from 'antd';

interface FollowModeFormProps {
  form: any;
  isAiMode?: boolean;
}

const FollowModeForm: React.FC<FollowModeFormProps> = ({ form, isAiMode = false }) => {
  const followMode = Form.useWatch('follow_mode', form);
  // 跟卖开关的值无需在此处计算逻辑，仅用于展示在跟单模式区域

  return (
    <>
      <Typography.Title level={5} style={{ margin: '0 0 8px 0', color: '#722ed1' }}>
        💰 {isAiMode ? 'AI信号交易配置' : '跟单模式配置'}
      </Typography.Title>
      
      <Row gutter={[8, 4]}>
        <Col span={6}>
          <Form.Item
            name="follow_mode"
            label={isAiMode ? "交易模式" : "跟单模式"}
            rules={[{ required: true, message: isAiMode ? '请选择交易模式' : '请选择跟单模式' }]}
          >
            <Radio.Group disabled={isAiMode}>
              <Radio value="Percentage" disabled={isAiMode}>
                {isAiMode ? '按百分比交易 (AI模式不支持)' : '按百分比跟单'}
              </Radio>
              <Radio value="FixedAmount">
                {isAiMode ? '固定金额交易' : '固定金额跟单'}
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Col>

        {followMode === 'Percentage' && (
          <Col span={4}>
            <Form.Item
              name="follow_percentage"
              label="跟单百分比 (%)"
              rules={[
                { required: followMode === 'Percentage', message: '请输入跟单百分比' }
              ]}
            >
              <Input
                style={{ width: '100%' }}
                placeholder="5.0"
                type="number"
              />
            </Form.Item>
          </Col>
        )}

        {followMode === 'FixedAmount' && (
          <Col span={4}>
            <Form.Item
              name="fixed_follow_amount_sol"
              label={isAiMode ? "固定交易金额 (SOL)" : "固定跟单金额 (SOL)"}
              rules={[
                { required: followMode === 'FixedAmount', message: isAiMode ? '请输入固定交易金额' : '请输入固定跟单金额' }
              ]}
            >
              <Input
                style={{ width: '100%' }}
                placeholder={isAiMode ? "0.01" : "0.001"}
                type="number"
              />
            </Form.Item>
          </Col>
        )}

        {/* 跟卖开关（AI模式下隐藏） */}
        {!isAiMode && (
          <Col span={4}>
            <Form.Item
              name="follow_sell_enabled"
              label="跟卖开关"
              valuePropName="checked"
              tooltip="开启后按leader卖出比例同步卖出"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
        )}
      </Row>
    </>
  );
};

export default FollowModeForm; 