import { useCallback } from 'react';
import ApiService from '../services/api';
import type { WalletConfig } from '../types/api';

// 本地钱包备注管理hook
export interface WalletRemark {
  address: string;
  protocol: 'pump' | 'bonk'; // 协议类型
  remark: string;
  updatedAt: number;
  source: 'server' | 'local'; // 备注来源：服务器或本地
}

const STORAGE_KEY = 'wallet_remarks_cache';

export const useWalletRemarks = () => {
  // 简化版hook，主要功能已移至服务器端WalletConfig.remark字段

  // 更新服务器端备注引用（从WalletConfig中同步）
  const updateServerRemarks = useCallback((walletConfigs: Record<string, WalletConfig>) => {
    // 简化逻辑：服务器端备注已经在WalletConfig.remark中，不需要复杂的本地缓存同步
    console.log(`📝 从服务器同步了 ${Object.values(walletConfigs).filter(c => c.remark).length} 个备注`);
  }, []);

  // 设置或更新钱包备注（同时更新服务器和本地）
  const setWalletRemark = useCallback(async (address: string, protocol: 'pump' | 'bonk', remark: string) => {
    const trimmedRemark = remark.trim();
    
    // 尝试更新服务器端（如果存在该钱包配置）
    try {
      // 先获取当前钱包配置
      const walletConfigs = await ApiService.getWalletConfigurations();
      // 查找对应地址和协议的配置
      const currentConfig = Object.values(walletConfigs).find(config => 
        config.wallet_address === address && (config.protocol || 'pump') === protocol
      );
      
      if (currentConfig) {
        // 更新服务器端备注
        const updatedConfig: WalletConfig = {
          ...currentConfig,
          remark: trimmedRemark || null
        };
        
        await ApiService.updateWalletConfiguration(updatedConfig);
        console.log(`📝 备注已同步到服务器: ${address} (${protocol})`);
      } else {
        console.log(`📝 钱包配置不存在: ${address} (${protocol})`);
      }
    } catch (error) {
      console.error('同步备注到服务器失败:', error);
      throw error; // 抛出错误让调用方知道失败了
    }
  }, []);

  // 删除钱包备注
  const removeWalletRemark = useCallback(async (address: string, protocol: 'pump' | 'bonk') => {
    // 尝试删除服务器端备注
    try {
      const walletConfigs = await ApiService.getWalletConfigurations();
      // 查找对应地址和协议的配置
      const currentConfig = Object.values(walletConfigs).find(config => 
        config.wallet_address === address && (config.protocol || 'pump') === protocol
      );
      
      if (currentConfig && currentConfig.remark) {
        const updatedConfig: WalletConfig = {
          ...currentConfig,
          remark: null
        };
        
        await ApiService.updateWalletConfiguration(updatedConfig);
        console.log(`📝 服务器备注已删除: ${address} (${protocol})`);
      }
    } catch (error) {
      console.error('删除服务器备注失败:', error);
      throw error;
    }
  }, []);

  // 兼容性函数：根据地址获取备注（返回格式化地址，因为真正的备注应该从WalletConfig.remark读取）
  const getWalletRemarkByAddress = useCallback((address: string): string => {
    // 返回格式化的地址作为默认显示，真正的备注应该从服务器端WalletConfig.remark读取
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }, []);

  // 简化版获取函数，主要用于向后兼容
  const getWalletRemark = useCallback((address: string): string => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }, []);

  const getWalletRemarkOrNull = useCallback((): string | null => {
    return null;
  }, []);

  const hasRemark = useCallback((): boolean => {
    return false;
  }, []);

  const getAllRemarks = useCallback(() => {
    return [];
  }, []);

  const clearAllRemarks = useCallback(() => {
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  const loadRemarks = useCallback(() => {
    // 空函数，向后兼容
  }, []);

  return {
    // 核心功能
    setWalletRemark,
    removeWalletRemark,
    updateServerRemarks,
    // 兼容性函数（主要返回格式化地址，真正备注从WalletConfig.remark读取）
    getWalletRemarkByAddress,
    getWalletRemark,
    getWalletRemarkOrNull,
    hasRemark,
    getAllRemarks,
    clearAllRemarks,
    loadRemarks,
    // 遗留数据
    remarks: {},
    isLoading: false,
  };
};
